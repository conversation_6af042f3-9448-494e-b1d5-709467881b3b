@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
  }
}

@layer components {
  .drag-area {
    @apply border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center transition-colors duration-200;
  }

  .drag-area.drag-over {
    @apply border-primary-500 bg-primary-50 dark:bg-primary-900/20;
  }

  .file-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 transition-all duration-200 hover:shadow-md;
  }

  .progress-bar {
    @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-500 to-primary-600 transition-all duration-300 ease-out;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
